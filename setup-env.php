<?php
/**
 * Environment Setup Helper
 * 
 * This script helps you create and configure your .env file
 * Run this once to set up your environment variables
 */

echo "🚀 Space Phoenix Portfolio - Environment Setup\n";
echo "==============================================\n\n";

$envFile = 'config/.env';
$exampleFile = 'config/.env.example';

// Check if .env already exists
if (file_exists($envFile)) {
    echo "⚠️  .env file already exists!\n";
    echo "Do you want to overwrite it? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    if (trim($line) !== 'y' && trim($line) !== 'Y') {
        echo "Setup cancelled.\n";
        exit;
    }
    fclose($handle);
}

// Copy example file
if (!file_exists($exampleFile)) {
    echo "❌ Error: .env.example file not found!\n";
    exit(1);
}

copy($exampleFile, $envFile);
echo "✅ Created .env file from template\n\n";

// Interactive setup
echo "🔐 Let's configure your environment variables:\n\n";

// reCAPTCHA Setup
echo "1. reCAPTCHA v3 Configuration\n";
echo "   Get your keys from: https://www.google.com/recaptcha/admin\n";
echo "   Site Key (public): ";
$handle = fopen("php://stdin", "r");
$siteKey = trim(fgets($handle));

echo "   Secret Key (private): ";
$secretKey = trim(fgets($handle));
fclose($handle);

// Google Forms Setup
echo "\n2. Google Forms Integration (optional)\n";
echo "   Form URL (ending with /formResponse): ";
$handle = fopen("php://stdin", "r");
$formUrl = trim(fgets($handle));

echo "   Name field ID (entry.XXXXXXX): ";
$nameField = trim(fgets($handle));

echo "   Email field ID (entry.XXXXXXX): ";
$emailField = trim(fgets($handle));

echo "   Message field ID (entry.XXXXXXX): ";
$messageField = trim(fgets($handle));
fclose($handle);

// Email Setup
echo "\n3. Email Configuration (optional)\n";
echo "   SMTP Username (your email): ";
$handle = fopen("php://stdin", "r");
$smtpUser = trim(fgets($handle));

echo "   SMTP Password (app password): ";
$smtpPass = trim(fgets($handle));
fclose($handle);

// Site Configuration
echo "\n4. Site Configuration\n";
echo "   Site URL (https://yourdomain.com): ";
$handle = fopen("php://stdin", "r");
$siteUrl = trim(fgets($handle));

echo "   Environment (development/production): ";
$environment = trim(fgets($handle));
fclose($handle);

// Update .env file
$envContent = file_get_contents($envFile);

// Replace values
$replacements = [
    'RECAPTCHA_ENABLED=true' => 'RECAPTCHA_ENABLED=' . ($siteKey ? 'true' : 'false'),
    'RECAPTCHA_SITE_KEY=your_recaptcha_v3_site_key_here' => 'RECAPTCHA_SITE_KEY=' . $siteKey,
    'RECAPTCHA_SECRET_KEY=your_recaptcha_v3_secret_key_here' => 'RECAPTCHA_SECRET_KEY=' . $secretKey,
    'GOOGLE_FORMS_ENABLED=true' => 'GOOGLE_FORMS_ENABLED=' . ($formUrl ? 'true' : 'false'),
    'GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse' => 'GOOGLE_FORMS_URL=' . $formUrl,
    'GOOGLE_FORMS_NAME_FIELD=entry.123456789' => 'GOOGLE_FORMS_NAME_FIELD=' . $nameField,
    'GOOGLE_FORMS_EMAIL_FIELD=entry.987654321' => 'GOOGLE_FORMS_EMAIL_FIELD=' . $emailField,
    'GOOGLE_FORMS_MESSAGE_FIELD=entry.456789123' => 'GOOGLE_FORMS_MESSAGE_FIELD=' . $messageField,
    'SMTP_USERNAME=<EMAIL>' => 'SMTP_USERNAME=' . $smtpUser,
    'SMTP_PASSWORD=your_app_password_here' => 'SMTP_PASSWORD=' . $smtpPass,
    'SITE_URL=https://yourdomain.com' => 'SITE_URL=' . ($siteUrl ?: 'http://localhost'),
    'ENVIRONMENT=production' => 'ENVIRONMENT=' . ($environment ?: 'development')
];

foreach ($replacements as $search => $replace) {
    $envContent = str_replace($search, $replace, $envContent);
}

file_put_contents($envFile, $envContent);

// Set secure permissions
chmod($envFile, 0600);

echo "\n✅ Environment setup complete!\n\n";

echo "📋 Summary:\n";
echo "   reCAPTCHA: " . ($siteKey ? "✅ Configured" : "❌ Not configured") . "\n";
echo "   Google Forms: " . ($formUrl ? "✅ Configured" : "❌ Not configured") . "\n";
echo "   Email: " . ($smtpUser ? "✅ Configured" : "❌ Not configured") . "\n";
echo "   Site URL: " . ($siteUrl ?: 'http://localhost') . "\n";
echo "   Environment: " . ($environment ?: 'development') . "\n\n";

echo "🔒 Security:\n";
echo "   ✅ .env file permissions set to 600 (secure)\n";
echo "   ⚠️  Make sure .env is in your .gitignore file\n";
echo "   ⚠️  Never commit .env to version control\n\n";

echo "🧪 Next Steps:\n";
echo "   1. Test your contact form\n";
echo "   2. Check browser console for reCAPTCHA messages\n";
echo "   3. Verify email delivery (if configured)\n";
echo "   4. Check Google Forms submissions (if configured)\n\n";

echo "🚀 Your Space Phoenix Portfolio is ready to launch!\n";
?>
