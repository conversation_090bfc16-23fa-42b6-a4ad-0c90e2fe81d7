<!-- HERO -->
<section id="hero" class="hero loading" aria-labelledby="hero-title">
  <div class="wrap hero-inner">
    <div>
      <p class="kicker">Backend Developer</p>
      <h1 id="hero-title" class="title">Building Robust Web Solutions.</h1>
      <p class="subtitle">Backend Developer with hands-on experience building and maintaining full-stack web applications, ERP systems, and CMS platforms. Passionate about creating efficient, scalable solutions using modern technologies.</p>
      <div class="cta">
        <a class="btn primary enhanced-hover" href="#projects" aria-describedby="projects-desc">View Projects</a>
        <span id="projects-desc" class="sr-only">Navigate to projects showcase section</span>
        <a class="btn enhanced-hover" href="#contact" aria-describedby="contact-desc">Get in Touch</a>
        <span id="contact-desc" class="sr-only">Navigate to contact form section</span>
      </div>
    </div>
    <div class="phoenix-wrap parallax-slow" data-parallax data-speed="0.05">
      <div class="phoenix-glow" aria-hidden="true"></div>
      <!-- Enhanced Phoenix SVG with particle effects -->
      <svg id="phoenix" viewBox="0 0 512 512" fill="none" stroke="url(#grad)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
        <defs>
          <linearGradient id="grad" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stop-color="var(--glow)"/>
            <stop offset="50%" stop-color="var(--flame)"/>
            <stop offset="100%" stop-color="var(--accent)"/>
          </linearGradient>
          <linearGradient id="fireGrad" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stop-color="var(--flame)" stop-opacity="0.8"/>
            <stop offset="50%" stop-color="var(--glow)" stop-opacity="0.6"/>
            <stop offset="100%" stop-color="var(--accent)" stop-opacity="0.3"/>
          </linearGradient>
          <filter id="phoenixGlow">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <radialGradient id="particleGrad" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stop-color="var(--glow)" stop-opacity="1"/>
            <stop offset="70%" stop-color="var(--flame)" stop-opacity="0.6"/>
            <stop offset="100%" stop-color="transparent" stop-opacity="0"/>
          </radialGradient>
        </defs>

        <!-- Particle system for phoenix fire -->
        <g id="phoenixParticles" opacity="0.7">
          <!-- Particles will be dynamically added here -->
        </g>

        <!-- Main phoenix body with enhanced paths -->
        <g filter="url(#phoenixGlow)">
          <path id="phoenixBody" d="M256 60c-38 62 6 116-20 168-20 38-72 42-116 26 62 26 112 6 144-36 32 42 82 62 144 36-44 16-96 12-116-26-26-52 18-106-20-168Z" fill="none" stroke="url(#grad)" stroke-width="2.5"/>
          <path id="phoenixWings" d="M116 248c38 20 72 38 92 70-18 16-44 20-68 14 28 10 52 26 62 52-18 2-36-2-50-10 34 30 66 60 104 78 38-18 70-48 104-78-14 8-32 12-50 10 10-26 34-42 62-52-24 6-50 2-68-14 20-32 54-50 92-70" fill="none" stroke="url(#grad)" stroke-width="2"/>
          <path id="phoenixTail" d="M248 126c-6 22-8 44-8 66m16-66c6 22 8 44 8 66" stroke="url(#fireGrad)" stroke-width="3" opacity="0.8"/>

          <!-- Additional flame details -->
          <path id="flameDetail1" d="M200 180c8-12 16-8 24 0 8 8 16 4 24-8" stroke="var(--flame)" stroke-width="1.5" opacity="0.6"/>
          <path id="flameDetail2" d="M264 180c8-12 16-8 24 0 8 8 16 4 24-8" stroke="var(--flame)" stroke-width="1.5" opacity="0.6"/>
        </g>

        <!-- Phoenix eye with glow -->
        <circle id="phoenixEye" cx="256" cy="140" r="3" fill="var(--glow)" opacity="0.9">
          <animate attributeName="opacity" values="0.9;1;0.9" dur="2s" repeatCount="indefinite"/>
        </circle>
      </svg>

      <!-- Particle canvas for advanced effects -->
      <canvas id="phoenixParticleCanvas" style="position: absolute; inset: 0; pointer-events: none; z-index: 1;"></canvas>
    </div>
  </div>
</section>
