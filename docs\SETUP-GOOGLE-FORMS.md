# 🚀 Google Forms Integration Setup Guide

This guide will help you set up Google Forms integration and reCAPTCHA for your Space Phoenix Portfolio contact form.

**Note**: Google Forms integration is handled **server-side by <PERSON><PERSON>** as a backup method. The primary contact handling is done by your PHP backend with Google Forms serving as a silent backup to Google Sheets.

## 📋 Prerequisites

- Google account
- Access to Google Forms
- Access to Google reCAPTCHA admin console
- Basic understanding of HTML form field inspection

## 🔧 Step 1: Create Google Form

1. **Go to Google Forms**
   - Visit [https://forms.google.com](https://forms.google.com)
   - Click "Create a new form" or use the "+" button

2. **Set up your form fields**
   Create exactly these fields in this order:
   - **Name** (Short answer)
   - **Email** (Short answer) 
   - **Message** (Paragraph)

3. **Configure form settings**
   - Click the gear icon (Settings)
   - Under "General" tab:
     - Uncheck "Collect email addresses" (we have our own email field)
     - Check "Limit to 1 response" if desired
   - Under "Presentation" tab:
     - Add a custom confirmation message: "Thank you! Your message has been received."

## 🔍 Step 2: Get Form Field IDs

### Method 1: Pre-filled Link (Recommended)
1. In your Google Form, click the three dots menu (⋮)
2. Select "Get pre-filled link"
3. Fill in sample data:
   - Name: "John Doe"
   - Email: "<EMAIL>"
   - Message: "Test message"
4. Click "Get link"
5. Copy the URL - it will look like:
   ```
   https://docs.google.com/forms/d/e/1FAIpQLSe.../viewform?usp=pp_url&entry.*********=John+Doe&entry.*********=<EMAIL>&entry.*********=Test+message
   ```
6. Extract the entry IDs:
   - Name field: `entry.*********`
   - Email field: `entry.*********`
   - Message field: `entry.*********`

### Method 2: Inspect Form HTML
1. Open your form in a browser
2. Right-click on each field and select "Inspect Element"
3. Look for the `name` attribute with format `entry.XXXXXXX`

## 📝 Step 3: Get Form Submission URL

1. From the pre-filled link URL, copy the form ID part:
   ```
   https://docs.google.com/forms/d/e/1FAIpQLSe.../viewform
   ```
2. Replace `viewform` with `formResponse`:
   ```
   https://docs.google.com/forms/d/e/1FAIpQLSe.../formResponse
   ```

## 🛡️ Step 4: Set up Google reCAPTCHA

1. **Go to reCAPTCHA Admin Console**
   - Visit [https://www.google.com/recaptcha/admin](https://www.google.com/recaptcha/admin)
   - Sign in with your Google account

2. **Create a new site**
   - Click "+" to add a new site
   - Label: "Space Phoenix Portfolio"
   - reCAPTCHA type: Select "reCAPTCHA v2" → "I'm not a robot" Checkbox
   - Domains: Add your domain (e.g., `yourdomain.com`, `localhost` for testing)
   - Accept the Terms of Service
   - Click "Submit"

3. **Get your keys**
   - Copy the **Site Key** (public key)
   - Copy the **Secret Key** (private key - keep this secure!)

## ⚙️ Step 5: Configure Your Portfolio

### Update config/google-forms.js
```javascript
const GOOGLE_FORMS_CONFIG = {
  enabled: true, // Change to true
  formUrl: 'https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse',
  fields: {
    name: 'entry.*********',     // Your actual name field ID
    email: 'entry.*********',    // Your actual email field ID
    message: 'entry.*********'   // Your actual message field ID
  }
};

const RECAPTCHA_CONFIG = {
  enabled: true, // Change to true
  siteKey: 'YOUR_RECAPTCHA_SITE_KEY', // Your actual site key
  theme: 'dark',
  size: 'normal'
};
```

### Update sections/contact.php
Replace `YOUR_RECAPTCHA_SITE_KEY` with your actual site key:
```html
<div class="g-recaptcha" data-sitekey="YOUR_ACTUAL_SITE_KEY" data-theme="dark"></div>
```

### Update config/.env (if using environment variables)
```env
# Google Forms Integration
GOOGLE_FORMS_ENABLED=true
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.*********
GOOGLE_FORMS_EMAIL_FIELD=entry.*********
GOOGLE_FORMS_MESSAGE_FIELD=entry.*********

# Google reCAPTCHA
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
```

## 🧪 Step 6: Test the Integration

1. **Test Google Form directly**
   - Submit a test entry through your Google Form
   - Check that it appears in the form responses

2. **Test your portfolio contact form**
   - Fill out the contact form on your website
   - Complete the reCAPTCHA
   - Submit the form
   - Check that:
     - You receive a thank you email
     - The submission appears in your Google Form responses
     - No errors appear in browser console

3. **Test reCAPTCHA**
   - Try submitting without completing reCAPTCHA
   - Verify that validation error appears

## 🔧 Troubleshooting

### Common Issues

**Form not submitting to Google Forms:**
- Verify the form URL ends with `/formResponse`
- Check that field IDs match exactly (case-sensitive)
- Ensure CORS is not blocking the request (this is expected with `no-cors` mode)

**reCAPTCHA not loading:**
- Check that the site key is correct
- Verify your domain is added to reCAPTCHA settings
- Check browser console for JavaScript errors

**Email not sending:**
- Verify SMTP settings in config
- Check server error logs
- Ensure PHP mail function is working

### Testing Tips

1. **Use browser developer tools**
   - Check Network tab for failed requests
   - Look for JavaScript errors in Console

2. **Check server logs**
   - Look in `logs/error.log` for PHP errors
   - Check web server error logs

3. **Test with simple data**
   - Use basic text without special characters
   - Test with a simple email address

## 🚀 Going Live

1. **Update domain settings**
   - Add your production domain to reCAPTCHA settings
   - Update `SITE_URL` in configuration

2. **Enable production mode**
   - Set `ENVIRONMENT=production` in config
   - Disable debug logging

3. **Monitor submissions**
   - Check Google Form responses regularly
   - Monitor email delivery
   - Review error logs

## 📧 Email Configuration

For the thank you emails to work, ensure your server has:
- PHP mail function enabled, OR
- SMTP configuration set up in config

The system will gracefully degrade if email fails - the form will still submit to Google Forms.

## 🔒 Security Notes

- Keep your reCAPTCHA secret key secure
- Never commit secret keys to version control
- Use environment variables for sensitive data
- Regularly monitor form submissions for spam

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review browser console and server logs
3. Test each component individually
4. Verify all configuration values are correct

The contact form is designed to be resilient - even if some components fail, the core functionality will continue to work.
