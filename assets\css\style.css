:root {
  --bg:#07070a;           /* deep space */
  --panel:#0c0f16;        /* panels */
  --text:#eaeaf2;         /* base text */
  --muted:#9aa0a6;        /* muted text */
  --flame:#ff6b35;        /* phoenix flame */
  --glow:#ffd56b;         /* soft glow */
  --accent:#6c63ff;       /* cosmic purple */
  --grid: 1200px;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  #starfield, .space-dust {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --bg: #000000;
    --panel: #1a1a1a;
    --text: #ffffff;
    --muted: #cccccc;
    --flame: #ff8800;
    --glow: #ffff00;
    --accent: #8888ff;
  }
}

/* Loading states */
.loading {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Focus styles for accessibility */
.btn:focus, input:focus, textarea:focus, [tabindex]:focus {
  outline: 2px solid var(--glow);
  outline-offset: 2px;
}

/* Form styles */
form {
  max-width: 100%;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
}

input, textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255,255,255,.12);
  border-radius: 8px;
  background: rgba(255,255,255,.03);
  color: var(--text);
  font-family: inherit;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  border-color: var(--glow);
  background: rgba(255,255,255,.06);
  box-shadow: 0 0 0 3px rgba(255,213,107,.1);
}

input.error, textarea.error {
  border-color: var(--flame);
  background: rgba(255,107,53,.05);
}

.field-error {
  color: var(--flame);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-message {
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.form-message.success {
  background: rgba(39,201,63,.1);
  border: 1px solid rgba(39,201,63,.3);
  color: #27c93f;
}

.form-message.error {
  background: rgba(255,107,53,.1);
  border: 1px solid rgba(255,107,53,.3);
  color: var(--flame);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-loading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,.3);
  border-top: 2px solid var(--glow);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* reCAPTCHA styling */
.g-recaptcha {
  transform: scale(0.95);
  transform-origin: 0 0;
  margin-bottom: 0.5rem;
}

/* Dark theme reCAPTCHA integration */
@media (prefers-color-scheme: dark) {
  .g-recaptcha {
    filter: invert(1) hue-rotate(180deg);
  }
}

/* reCAPTCHA container styling */
.g-recaptcha > div {
  border-radius: 8px !important;
  overflow: hidden;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--panel);
  color: var(--text);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

html, body {
  height: 100%;
  background: radial-gradient(1200px 800px at 70% -10%, rgba(108,99,255,.15), transparent 60%),
              radial-gradient(800px 600px at 10% 10%, rgba(255,107,53,.12), transparent 60%),
              var(--bg);
  color: var(--text);
  margin: 0;
  font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
  overflow-x: hidden;
}

a { color: var(--glow); text-decoration: none; }
a:hover { text-decoration: underline; }

/* Global layout */
header {
  position: fixed; inset-inline: 0; top: 0; z-index: 50;
  backdrop-filter: saturate(120%) blur(10px);
  background: linear-gradient(to bottom, rgba(7,7,10,.75), transparent);
  border-bottom: 1px solid rgba(255,255,255,.06);
}

.nav {
  max-width: var(--grid); margin: 0 auto; padding: .6rem 1rem; display:flex; align-items:center; justify-content:space-between;
}

.brand { display:flex; gap:.6rem; align-items:center; font-weight:700; letter-spacing:.3px; }
.dot { width: 10px; height: 10px; border-radius: 9999px; background: linear-gradient(180deg, var(--glow), var(--flame)); box-shadow: 0 0 12px var(--glow); }

/* Brand Logo Styles */
.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.1) rotate(5deg);
}

.brand-logo img {
  animation: brandGlow 3s ease-in-out infinite;
}

@keyframes brandGlow {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(255, 213, 107, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(255, 213, 107, 0.8));
  }
}
.menu { display:flex; gap:1rem; font-size:.95rem; }
.menu a { opacity:.9 }

main { position: relative; }

/* Starfield canvas */
#starfield { position: fixed; inset:0; z-index:-2; display:block; }

/* Subtle dust overlay for depth */
.space-dust {
  position: fixed; inset:0; pointer-events:none; z-index:-1;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><filter id="f"><feTurbulence baseFrequency="0.9" numOctaves="2" type="fractalNoise"/></filter><rect width="200" height="200" opacity="0.03" filter="url(%23f)"/></svg>') repeat;
  mix-blend-mode: screen;
}

/* Sections */
section { padding: 7rem 1rem; }
.wrap { max-width: var(--grid); margin: 0 auto; }

/* HERO */
.hero { display:grid; min-height: 100dvh; place-items:center; position: relative; }
.hero-inner { display:grid; grid-template-columns: 1.2fr 1fr; gap: 3rem; align-items:center; width: 100%; }
.hero p.kicker { color: var(--muted); text-transform: uppercase; font-size: .8rem; letter-spacing: .2em; margin: 0 0 .6rem; }
.title { font-size: clamp(2.2rem, 5vw, 4rem); line-height: 1.05; margin: 0 0 1rem; font-weight: 800; }
.subtitle { color: var(--muted); font-size: clamp(1rem, 2vw, 1.2rem); max-width: 60ch; }

.cta { margin-top: 1.5rem; display:flex; gap:.8rem; flex-wrap: wrap; }
.btn { border:1px solid rgba(255,255,255,.12); background: rgba(255,255,255,.03); color: var(--text); padding:.8rem 1rem; border-radius: 16px; font-weight:600; cursor:pointer; }
.btn.primary { background: linear-gradient(180deg, rgba(255,213,107,.22), rgba(255,107,53,.22)); border-color: rgba(255,213,107,.45); box-shadow: 0 6px 30px rgba(255,213,107,.18); }
.btn:hover { transform: translateY(-2px); }

.phoenix-wrap { position: relative; }
.phoenix-glow { position:absolute; inset: -10%; filter: blur(40px); opacity:.35; background: radial-gradient(closest-side, var(--glow), transparent 65%); border-radius: 50%; }
img#phoenix {
  width: min(480px, 38vw);
  height: auto;
  display: block;
  transform-origin: 50% 50%;
  border-radius: 12px;
  filter: drop-shadow(0 0 20px rgba(255, 213, 107, 0.3));
  transition: all 0.3s ease;
  animation: phoenixFly 8s ease-in-out infinite, phoenixGlow 4s ease-in-out infinite;
  /* Ensure image maintains aspect ratio and looks good */
  object-fit: cover;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 213, 107, 0.1);
}

/* Flying Animation Keyframes */
@keyframes phoenixFly {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  12.5% {
    transform: translateY(-8px) translateX(5px) rotate(1deg) scale(1.01);
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(2deg) scale(1.02);
  }
  37.5% {
    transform: translateY(-12px) translateX(15px) rotate(0deg) scale(1.01);
  }
  50% {
    transform: translateY(-8px) translateX(20px) rotate(-1deg) scale(1);
  }
  62.5% {
    transform: translateY(-18px) translateX(15px) rotate(1deg) scale(1.01);
  }
  75% {
    transform: translateY(-20px) translateX(10px) rotate(3deg) scale(1.02);
  }
  87.5% {
    transform: translateY(-10px) translateX(5px) rotate(1deg) scale(1.01);
  }
}

/* Phoenix glow animation */
@keyframes phoenixGlow {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(255, 213, 107, 0.3));
  }
  25% {
    filter: drop-shadow(0 0 25px rgba(255, 213, 107, 0.4));
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(255, 213, 107, 0.5));
  }
  75% {
    filter: drop-shadow(0 0 25px rgba(255, 213, 107, 0.4));
  }
}

/* Wing flapping effect */
@keyframes wingFlap {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(255, 213, 107, 0.3)) brightness(1);
  }
  50% {
    filter: drop-shadow(0 0 25px rgba(255, 213, 107, 0.5)) brightness(1.1);
  }
}

/* Enhanced flying animation on hover */
img#phoenix:hover {
  animation: phoenixFlyHover 4s ease-in-out infinite, wingFlap 2s ease-in-out infinite;
}

@keyframes phoenixFlyHover {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-25px) translateX(15px) rotate(5deg) scale(1.05);
  }
  50% {
    transform: translateY(-12px) translateX(30px) rotate(-2deg) scale(1.02);
  }
  75% {
    transform: translateY(-30px) translateX(15px) rotate(6deg) scale(1.05);
  }
}

/* ABOUT */
.panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.00)); border:1px solid rgba(255,255,255,.06); border-radius: 20px; padding: 1.2rem; }
.about { display:grid; gap: 1.2rem; grid-template-columns: 1.2fr .8fr; align-items: stretch; }
.terminal { background: #0a0d13; border-radius: 14px; border:1px solid rgba(255,255,255,.06); position: relative; overflow:hidden; }
.terminal .bar { display:flex; gap:.5rem; align-items:center; padding:.6rem .8rem; border-bottom:1px solid rgba(255,255,255,.06); background: linear-gradient(180deg, rgba(255,255,255,.04), transparent); }
.status-dot { width:10px; height:10px; border-radius:9999px; background:#ff5f56; box-shadow: 0 0 8px rgba(255,95,86,.8); }
.status-dot.yellow { background:#ffbd2e; box-shadow:0 0 8px rgba(255,189,46,.8); }
.status-dot.green { background:#27c93f; box-shadow:0 0 8px rgba(39,201,63,.8); }
.terminal pre { margin: 0; padding: 1rem; font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; color: #c8e1ff; font-size: .95rem; min-height: 220px; white-space: pre-wrap; }

/* ENHANCED PROJECTS */
.projects-grid { display:grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; perspective: 1000px; }
.card {
  position:relative; overflow:hidden; border-radius: 20px; background: var(--panel);
  border:1px solid rgba(255,255,255,.08); padding:1.5rem; min-height: 280px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  cursor: pointer;
}
.card:hover {
  transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 20px 60px rgba(0,0,0,.4), 0 0 0 1px rgba(255,213,107,.2);
  border-color: rgba(255,213,107,.3);
}
.card h3 { margin:.4rem 0 .6rem; font-size: 1.3rem; }
.card p { color: var(--muted); line-height: 1.6; }
.card .shine {
  position:absolute; inset:-30%;
  background: conic-gradient(from 0deg, rgba(255,213,107,.12), transparent 25%, transparent 75%, rgba(255,213,107,.12));
  transform: rotate(0deg); opacity:.6; pointer-events:none;
  transition: opacity 0.3s ease;
}
.card:hover .shine { opacity: 1; }

.card .tech-stack {
  display: flex; flex-wrap: wrap; gap: 0.4rem; margin-top: 1rem;
}
.card .tech-badge {
  background: rgba(255,255,255,.06); border: 1px solid rgba(255,255,255,.1);
  padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;
  color: var(--glow); transition: all 0.3s ease;
}
.card:hover .tech-badge {
  background: rgba(255,213,107,.1); border-color: rgba(255,213,107,.3);
  transform: translateY(-2px);
}

.card .project-icon {
  position: absolute; top: 1rem; right: 1rem;
  width: 40px; height: 40px; border-radius: 50%;
  background: linear-gradient(135deg, var(--glow), var(--flame));
  display: flex; align-items: center; justify-content: center;
  font-size: 1.2rem; opacity: 0.8;
  transition: all 0.3s ease;
}
.card:hover .project-icon {
  transform: rotate(360deg) scale(1.1);
  opacity: 1;
}

/* SKILLS */
.skills { display:grid; grid-template-columns: .9fr 1.1fr; gap: 2rem; align-items:center; }
.skill-cloud { position: relative; aspect-ratio: 1/1; }
.skill-cloud svg { width: 100%; height: auto; display:block; }
.badge { display:inline-flex; gap:.5rem; align-items:center; background: rgba(255,255,255,.04); border:1px solid rgba(255,255,255,.08); padding:.5rem .7rem; border-radius: 9999px; margin: .25rem; font-size: .95rem; }

/* CONTACT */
form { display:grid; gap: .9rem; }
label { font-size:.9rem; color: var(--muted); }
input, textarea { width:100%; background: rgba(255,255,255,.03); border:1px solid rgba(255,255,255,.1); color: var(--text); border-radius: 12px; padding:.8rem; outline: none; }
input:focus, textarea:focus { border-color: var(--glow); box-shadow: 0 0 0 3px rgba(255,213,107,.15); }
textarea { min-height: 140px; }

footer { padding: 2rem 1rem 3rem; color: var(--muted); text-align:center; }

/* Utils */
.muted { color: var(--muted); }
.grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap: 1rem; }

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  :root { --grid: 95%; }
  .projects-grid { grid-template-columns: repeat(2, 1fr); gap: 1.2rem; }
}

@media (max-width: 1000px) {
  .hero-inner { grid-template-columns: 1fr; text-align:center; gap: 2rem; }
  .phoenix-wrap { order:-1; }
  .about { grid-template-columns: 1fr; }
  .skills { grid-template-columns: 1fr; text-align: center; }
  .skill-cloud { max-width: 300px; margin: 0 auto; }

  /* Reduce motion on tablets */
  .card:hover { transform: translateY(-4px); }
}

@media (max-width: 768px) {
  section { padding: 4rem 1rem; }
  .title { font-size: clamp(1.8rem, 6vw, 2.5rem); }
  .projects-grid { grid-template-columns: 1fr; gap: 1rem; }
  .card { padding: 1.2rem; min-height: 240px; }
  .cta { flex-direction: column; align-items: center; }
  .btn { width: 100%; max-width: 280px; text-align: center; }

  /* Mobile navigation improvements */
  .nav { padding: 0.8rem 1rem; }
  .menu { gap: 0.8rem; font-size: 0.9rem; }

  /* Touch-friendly sizing */
  .card .tech-badge { padding: 0.4rem 0.8rem; }
  input, textarea { padding: 1rem; font-size: 16px; } /* Prevent zoom on iOS */
}

@media (max-width: 480px) {
  .hero { min-height: 90vh; }
  .title { font-size: 2rem; line-height: 1.1; }
  .subtitle { font-size: 1rem; }
  section { padding: 3rem 0.8rem; }
  .card { padding: 1rem; min-height: 200px; }
  .nav { flex-direction: column; gap: 1rem; text-align: center; }
  .menu { justify-content: center; flex-wrap: wrap; }

  /* Adjust brand logo for mobile */
  .brand-logo img {
    width: 20px;
    height: 20px;
  }

  /* Simplified animations for mobile */
  .card:hover { transform: none; box-shadow: 0 8px 25px rgba(0,0,0,.3); }
  .phoenix-wrap { max-width: 280px; margin: 0 auto; }
  img#phoenix {
    width: min(280px, 90vw);
    animation: phoenixFlyMobile 6s ease-in-out infinite;
  }

  /* Simplified mobile flying animation */
  @keyframes phoenixFlyMobile {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-8px) rotate(1deg);
    }
    50% {
      transform: translateY(-4px) rotate(-0.5deg);
    }
    75% {
      transform: translateY(-12px) rotate(1.5deg);
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .card:hover { transform: none; }
  .card:active { transform: scale(0.98); }
  .btn:hover { transform: none; }
  .btn:active { transform: scale(0.95); }

  /* Disable complex animations on touch devices */
  .card .shine { display: none; }

  /* Simplified phoenix animation for touch devices */
  img#phoenix {
    animation: phoenixFlyTouch 8s ease-in-out infinite;
  }

  @keyframes phoenixFlyTouch {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dot { box-shadow: 0 0 8px var(--glow); }
  .status-dot { box-shadow: 0 0 6px rgba(255,95,86,.6); }
}

/* Landscape mobile orientation */
@media (max-height: 500px) and (orientation: landscape) {
  .hero { min-height: 100vh; padding: 2rem 1rem; }
  .hero-inner { gap: 1.5rem; }
  section { padding: 3rem 1rem; }
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Custom Cursor Effects */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 213, 107, 0.8);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
  transform: translate(-50%, -50%);
}

.custom-cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 213, 107, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.cursor-hover .custom-cursor {
  transform: translate(-50%, -50%) scale(1.5);
  background: rgba(255, 107, 53, 0.9);
}

.cursor-hover .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(1.5);
  border-color: rgba(255, 107, 53, 0.5);
}

.cursor-text .custom-cursor {
  transform: translate(-50%, -50%) scale(0.5);
}

.cursor-text .custom-cursor-follower {
  transform: translate(-50%, -50%) scale(2);
  border-color: rgba(108, 99, 255, 0.5);
}

/* Hide default cursor on interactive elements */
body.custom-cursor-enabled {
  cursor: none;
}

body.custom-cursor-enabled a,
body.custom-cursor-enabled button,
body.custom-cursor-enabled .btn,
body.custom-cursor-enabled .card {
  cursor: none;
}

/* Scroll Progress Indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, var(--glow), var(--flame), var(--accent));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  background: var(--panel);
  border: 2px solid var(--accent);
  border-radius: 50%;
  color: var(--glow);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px) scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 213, 107, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.scroll-to-top svg {
  width: 1.5rem;
  height: 1.5rem;
  transition: transform 0.2s ease;
}

.scroll-to-top:hover {
  background: rgba(255, 213, 107, 0.1);
  border-color: var(--glow);
  color: var(--flame);
  transform: translateY(0) scale(1.05);
  box-shadow:
    0 6px 25px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(255, 213, 107, 0.4);
}

.scroll-to-top:hover svg {
  transform: translateY(-2px);
}

.scroll-to-top:active {
  transform: translateY(0) scale(0.95);
}

.scroll-to-top:focus {
  outline: 2px solid var(--glow);
  outline-offset: 2px;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  animation: scrollToTopAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Subtle pulse effect when first appearing */
.scroll-to-top.visible.first-appear {
  animation: scrollToTopAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards,
             subtlePulse 2s ease-in-out 0.4s;
}

@keyframes scrollToTopAppear {
  0% {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.8) rotate(-180deg);
  }
  100% {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1) rotate(0deg);
  }
}

@keyframes subtlePulse {
  0%, 100% {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 213, 107, 0.2);
  }
  50% {
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(255, 213, 107, 0.4);
  }
}

/* Mobile responsiveness for scroll to top button */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 3rem;
    height: 3rem;
  }

  .scroll-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 2.75rem;
    height: 2.75rem;
  }

  .scroll-to-top svg {
    width: 1.125rem;
    height: 1.125rem;
  }
}

/* Enhanced Hover Effects */
.enhanced-hover {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.enhanced-hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 213, 107, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.enhanced-hover:hover::before {
  width: 200%;
  height: 200%;
}

.enhanced-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 213, 107, 0.2);
}

/* Parallax Elements */
.parallax-element {
  transition: transform 0.1s ease-out;
}

.parallax-slow {
  transition: transform 0.2s ease-out;
}

.parallax-fast {
  transition: transform 0.05s ease-out;
}

/* Mobile and Touch Device Optimizations */
@media (max-width: 768px) {
  .custom-cursor,
  .custom-cursor-follower {
    display: none !important;
  }

  body.custom-cursor-enabled {
    cursor: auto;
  }

  body.custom-cursor-enabled a,
  body.custom-cursor-enabled button,
  body.custom-cursor-enabled .btn,
  body.custom-cursor-enabled .card {
    cursor: pointer;
  }

  .parallax-element {
    transform: none !important;
  }

  .enhanced-hover:hover {
    transform: none;
  }

  .enhanced-hover:active {
    transform: scale(0.98);
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .custom-cursor,
  .custom-cursor-follower,
  .scroll-progress {
    display: none !important;
  }

  .scroll-to-top {
    transition: opacity 0.2s ease !important;
    animation: none !important;
  }

  .scroll-to-top:hover {
    transform: none !important;
  }

  .scroll-to-top svg {
    transform: none !important;
  }

  .parallax-element,
  .enhanced-hover,
  #phoenix,
  .phoenix-wrap {
    transform: none !important;
    transition: none !important;
  }

  .enhanced-hover::before {
    display: none;
  }
}

/* Performance Optimizations */
.parallax-element,
.enhanced-hover,
#phoenix,
.phoenix-wrap {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth Scroll Enhancement */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Loading Animation for Enhanced Elements */
.enhanced-hover,
.parallax-element {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (prefers-reduced-motion: reduce) {
  .enhanced-hover,
  .parallax-element {
    animation: none;
    opacity: 1;
    transform: none;
  }

  /* Disable phoenix flying animations for reduced motion */
  img#phoenix {
    animation: none !important;
  }

  img#phoenix:hover {
    animation: none !important;
  }

  /* Disable brand logo animations for reduced motion */
  .brand-logo img {
    animation: none !important;
  }

  .brand-logo:hover {
    transform: none !important;
  }
}
