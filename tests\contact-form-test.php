<?php
/**
 * Contact Form Testing Script
 * 
 * This script tests the contact form functionality including:
 * - CSRF token generation and validation
 * - Input validation and sanitization
 * - Rate limiting
 * - reCAPTCHA integration
 * - Database operations
 * - Email functionality
 */

// Define access constant
define('PORTFOLIO_ACCESS', true);

// Include configuration
require_once '../config/config.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Test Suite</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-title { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .test-result.pass { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-result.fail { background: #f8d7da; border: 1px solid #f5c6cb; }
        .test-result.skip { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>🚀 Space Phoenix Portfolio - Contact Form Test Suite</h1>
    
    <?php
    $testResults = [];
    
    // Test 1: Configuration Validation
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test 1: Configuration Validation</h2>';
    
    $configTests = [
        'Site configuration' => !empty(getConfig('site.name')),
        'Database configuration' => !empty(getConfig('database.host')),
        'Google Forms configuration' => !empty(getConfig('google_forms.url')),
        'reCAPTCHA configuration' => !empty(getConfig('recaptcha.site_key')),
        'Security configuration' => !empty(getConfig('security.csrf_token_name'))
    ];
    
    foreach ($configTests as $test => $result) {
        $class = $result ? 'pass' : 'fail';
        $status = $result ? '✅ PASS' : '❌ FAIL';
        echo "<div class='test-result $class'>$test: $status</div>";
        $testResults[] = $result;
    }
    echo '</div>';
    
    // Test 2: Function Availability
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test 2: Function Availability</h2>';
    
    $functionTests = [
        'sanitizeInput' => function_exists('sanitizeInput'),
        'generateCSRFToken' => function_exists('generateCSRFToken'),
        'verifyCSRFToken' => function_exists('verifyCSRFToken'),
        'getConfig' => function_exists('getConfig'),
        'logError' => function_exists('logError')
    ];
    
    foreach ($functionTests as $func => $exists) {
        $class = $exists ? 'pass' : 'fail';
        $status = $exists ? '✅ PASS' : '❌ FAIL';
        echo "<div class='test-result $class'>Function $func: $status</div>";
        $testResults[] = $exists;
    }
    echo '</div>';
    
    // Test 3: CSRF Token Generation
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test 3: CSRF Token Generation</h2>';
    
    session_start();
    try {
        $token1 = generateCSRFToken();
        $token2 = generateCSRFToken();
        
        $tokenValid = !empty($token1) && strlen($token1) === 64;
        $tokensUnique = $token1 !== $token2;
        $sessionSet = isset($_SESSION['csrf_token']);
        
        echo "<div class='test-result " . ($tokenValid ? 'pass' : 'fail') . "'>Token generation: " . ($tokenValid ? '✅ PASS' : '❌ FAIL') . "</div>";
        echo "<div class='test-result " . ($tokensUnique ? 'pass' : 'fail') . "'>Token uniqueness: " . ($tokensUnique ? '✅ PASS' : '❌ FAIL') . "</div>";
        echo "<div class='test-result " . ($sessionSet ? 'pass' : 'fail') . "'>Session storage: " . ($sessionSet ? '✅ PASS' : '❌ FAIL') . "</div>";
        
        $testResults[] = $tokenValid && $tokensUnique && $sessionSet;
    } catch (Exception $e) {
        echo "<div class='test-result fail'>CSRF token generation failed: " . htmlspecialchars($e->getMessage()) . "</div>";
        $testResults[] = false;
    }
    echo '</div>';
    
    // Test 4: Input Sanitization
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test 4: Input Sanitization</h2>';
    
    $testInputs = [
        '<script>alert("xss")</script>' => '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;',
        'Normal text' => 'Normal text',
        '  Whitespace  ' => 'Whitespace',
        'Special chars: <>&"' => 'Special chars: &lt;&gt;&amp;&quot;'
    ];
    
    $sanitizationPassed = true;
    foreach ($testInputs as $input => $expected) {
        $result = sanitizeInput($input);
        $passed = $result === $expected;
        $sanitizationPassed = $sanitizationPassed && $passed;
        
        $class = $passed ? 'pass' : 'fail';
        $status = $passed ? '✅ PASS' : '❌ FAIL';
        echo "<div class='test-result $class'>Input: " . htmlspecialchars($input) . " → " . htmlspecialchars($result) . " $status</div>";
    }
    $testResults[] = $sanitizationPassed;
    echo '</div>';
    
    // Test 5: Database Connection (if configured)
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test 5: Database Connection</h2>';
    
    $dbHost = getConfig('database.host');
    $dbName = getConfig('database.name');
    $dbUser = getConfig('database.username');
    $dbPass = getConfig('database.password');
    
    if ($dbHost && $dbName && $dbUser) {
        try {
            $pdo = new PDO(
                "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4",
                $dbUser,
                $dbPass,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            echo "<div class='test-result pass'>Database connection: ✅ PASS</div>";
            
            // Test table existence
            $stmt = $pdo->query("SHOW TABLES LIKE 'contact_submissions'");
            $tableExists = $stmt->rowCount() > 0;
            echo "<div class='test-result " . ($tableExists ? 'pass' : 'fail') . "'>Contact submissions table: " . ($tableExists ? '✅ PASS' : '❌ FAIL') . "</div>";
            
            $testResults[] = $tableExists;
        } catch (Exception $e) {
            echo "<div class='test-result fail'>Database connection failed: " . htmlspecialchars($e->getMessage()) . "</div>";
            $testResults[] = false;
        }
    } else {
        echo "<div class='test-result skip'>Database not configured - ⚠️ SKIP</div>";
        $testResults[] = true; // Don't fail if not configured
    }
    echo '</div>';
    
    // Test Summary
    echo '<div class="test-container">';
    echo '<h2 class="test-title">Test Summary</h2>';
    
    $totalTests = count($testResults);
    $passedTests = array_sum($testResults);
    $failedTests = $totalTests - $passedTests;
    
    $overallStatus = $failedTests === 0 ? 'pass' : 'fail';
    $overallText = $failedTests === 0 ? '✅ ALL TESTS PASSED' : "❌ $failedTests TESTS FAILED";
    
    echo "<div class='test-result $overallStatus'>";
    echo "<h3>$overallText</h3>";
    echo "<p>Passed: $passedTests / $totalTests</p>";
    echo "</div>";
    
    if ($failedTests > 0) {
        echo "<div class='test-result fail'>";
        echo "<h4>⚠️ Action Required:</h4>";
        echo "<ul>";
        echo "<li>Review failed tests above</li>";
        echo "<li>Check configuration in config/config.php</li>";
        echo "<li>Ensure database is set up if using database features</li>";
        echo "<li>Verify environment variables are properly set</li>";
        echo "</ul>";
        echo "</div>";
    }
    echo '</div>';
    ?>
    
    <div class="test-container">
        <h2 class="test-title">Manual Testing</h2>
        <p>After fixing any failed tests above, manually test:</p>
        <ul>
            <li>Submit the contact form with valid data</li>
            <li>Try submitting without reCAPTCHA (should fail)</li>
            <li>Submit multiple times rapidly (should trigger rate limiting)</li>
            <li>Check that emails are sent (if configured)</li>
            <li>Verify Google Forms integration (if configured)</li>
        </ul>
    </div>
</body>
</html>
