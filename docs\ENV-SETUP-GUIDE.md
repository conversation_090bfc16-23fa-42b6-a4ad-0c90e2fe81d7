# 🔐 Environment Variables Setup Guide

## 🎯 **QUICK SETUP**

### Step 1: Create .env File
```bash
# Copy the example file
cp config/.env.example config/.env

# Edit with your actual values
nano config/.env
```

### Step 2: Add Your Keys
Replace the placeholder values in `config/.env` with your actual credentials:

```env
# Google reCAPTCHA v3 (MOST IMPORTANT)
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=your_actual_site_key_here
RECAPTCHA_SECRET_KEY=your_actual_secret_key_here

# Google Forms Integration
GOOGLE_FORMS_ENABLED=true
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.*********
GOOGLE_FORMS_EMAIL_FIELD=entry.*********
GOOGLE_FORMS_MESSAGE_FIELD=entry.*********

# Email Configuration (Optional)
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Database (Optional)
DB_PASSWORD=your_secure_password_here

# Site Configuration
SITE_URL=https://yourdomain.com
ENVIRONMENT=production
```

## 🛡️ **CRITICAL: reCAPTCHA KEYS**

### Get Your Keys
1. Go to [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. Click **"+"** to create new site
3. Choose **reCAPTCHA v3**
4. Add domains:
   - `localhost` (for development)
   - `yourdomain.com` (for production)
5. Copy your keys:
   - **Site Key** (public) → `RECAPTCHA_SITE_KEY`
   - **Secret Key** (private) → `RECAPTCHA_SECRET_KEY`

### Example Keys Format
```env
# Real keys look like this:
RECAPTCHA_SITE_KEY=6LdXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
RECAPTCHA_SECRET_KEY=6LdYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
```

## 📧 **EMAIL CONFIGURATION**

### Gmail SMTP Setup
1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Add to .env**:
   ```env
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your_16_character_app_password
   ```

## 📝 **GOOGLE FORMS INTEGRATION**

### Get Form Details
1. **Create Google Form** with Name, Email, Message fields
2. **Get Form URL**:
   - Click Send → Copy link
   - Replace `viewform` with `formResponse`
3. **Get Field IDs**:
   - Use pre-filled link method
   - Or inspect form HTML for `entry.XXXXXXX`

### Example Configuration
```env
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/1FAIpQLSdXXXXXX/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.*********
GOOGLE_FORMS_EMAIL_FIELD=entry.*********
GOOGLE_FORMS_MESSAGE_FIELD=entry.*********
```

## 🗄️ **DATABASE SETUP (OPTIONAL)**

### MySQL Configuration
```env
DB_HOST=localhost
DB_NAME=portfolio_db
DB_USERNAME=portfolio_user
DB_PASSWORD=your_secure_password_here
```

### Create Database
```sql
CREATE DATABASE portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'portfolio_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
GRANT ALL PRIVILEGES ON portfolio_db.* TO 'portfolio_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🔒 **SECURITY BEST PRACTICES**

### File Permissions
```bash
# Secure the .env file
chmod 600 config/.env

# Make sure it's not readable by web server
echo "config/.env" >> .gitignore
```

### Never Commit Secrets
```bash
# Check .gitignore includes:
config/.env
*.env
.env.*
```

### Environment-Specific Values
```env
# Development
ENVIRONMENT=development
SITE_URL=http://localhost

# Production
ENVIRONMENT=production
SITE_URL=https://yourdomain.com
```

## ✅ **VERIFICATION CHECKLIST**

### After Setup, Check:
- [ ] `.env` file exists in `config/` directory
- [ ] reCAPTCHA keys are real (not test keys)
- [ ] Google Forms URL ends with `/formResponse`
- [ ] Email credentials are app passwords (not regular passwords)
- [ ] File permissions are secure (`chmod 600`)
- [ ] `.env` is in `.gitignore`

### Test Your Setup:
1. **Load contact form** - should show reCAPTCHA badge
2. **Submit form** - should work without errors
3. **Check console** - should show "reCAPTCHA v3 protection enabled"
4. **Verify emails** - should receive thank you email
5. **Check Google Sheets** - should see form submission

## 🚨 **TROUBLESHOOTING**

### Common Issues:

**"reCAPTCHA disabled"**
- Check `RECAPTCHA_ENABLED=true` in `.env`
- Verify site key is not empty

**"Invalid site key"**
- Ensure you're using real keys, not test keys
- Check domain is added to reCAPTCHA settings

**"Email not sending"**
- Use app password, not regular password
- Enable 2-factor authentication first

**"Google Forms not working"**
- Verify URL ends with `/formResponse`
- Check field IDs are correct

## 📁 **FILE STRUCTURE**
```
config/
├── .env.example          # Template file
├── .env                  # Your actual secrets (DO NOT COMMIT)
├── config.php           # Loads environment variables
└── google-forms.js      # Frontend configuration
```

## 🎯 **PRIORITY ORDER**

1. **reCAPTCHA Keys** (Essential for spam protection)
2. **Google Forms** (Backup submission method)
3. **Email SMTP** (Thank you emails)
4. **Database** (Optional local storage)

Start with reCAPTCHA keys - everything else is optional! 🚀
