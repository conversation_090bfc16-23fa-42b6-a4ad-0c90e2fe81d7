<!-- ABOUT -->
<section id="about" class="loading">
  <div class="wrap about">
    <div class="panel">
      <h2>About Me</h2>
      <p class="muted">Currently working as a Backend Developer at Pixel Perfect Strategies (Dec 2024 – Present). I specialize in building full-stack web applications, ERP systems, and CMS platforms with a focus on clean code and efficient database design.</p>

      <h3 style="margin-top: 1.5rem; margin-bottom: 0.5rem; font-size: 1.1rem;">Education</h3>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 0.3rem;">• B.Tech in Computer Science & Engineering - NITRA Technical Campus, Ghaziabad (2025)</p>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 1rem;">• Diploma in Information Technology - Hewett Polytechnic, Lucknow (2022)</p>

      <h3 style="margin-bottom: 0.5rem; font-size: 1.1rem;">Certifications</h3>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 0.3rem;">• PHP Technology Training</p>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 0.3rem;">• Python Data Structures</p>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 0.3rem;">• Programming for Everybody</p>
      <p class="muted" style="font-size: 0.9rem; margin-bottom: 1rem;">• Django Web Framework</p>

      <div class="grid-2" style="margin-top: .6rem;">
        <div class="badge">PHP</div>
        <div class="badge">JavaScript</div>
        <div class="badge">Python</div>
        <div class="badge">C/C++</div>
        <div class="badge">Django</div>
        <div class="badge">Bootstrap</div>
        <div class="badge">MySQL</div>
        <div class="badge">Git</div>
        <div class="badge">GitHub</div>
        <div class="badge">VS Code</div>
        <div class="badge">Postman</div>
        <div class="badge">Ajax</div>
      </div>
    </div>
    <div class="terminal">
      <div class="bar">
        <span class="status-dot"></span>
        <span class="status-dot yellow"></span>
        <span class="status-dot green"></span>
        <span style="margin-left:.5rem; opacity:.8;">~/phoenix/run.sh</span>
      </div>
      <pre id="terminalText"></pre>
    </div>
  </div>
</section>
