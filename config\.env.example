# Space Phoenix Portfolio Environment Configuration
# Copy this file to .env and update with your actual values
# NEVER commit the .env file to version control

# Database Configuration
DB_HOST=localhost
DB_NAME=portfolio_db
DB_USERNAME=portfolio_user
DB_PASSWORD=your_secure_password_here

# Email Configuration (for contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Google Analytics
GA_ID=GA_MEASUREMENT_ID

# Security
SECRET_KEY=your_secret_key_here

# Site Configuration
SITE_URL=https://yourdomain.com
ENVIRONMENT=production

# Google Forms Integration
GOOGLE_FORMS_ENABLED=true
GOOGLE_FORMS_URL=https://docs.google.com/forms/d/e/YOUR_FORM_ID/formResponse
GOOGLE_FORMS_NAME_FIELD=entry.123456789
GOOGLE_FORMS_EMAIL_FIELD=entry.987654321
GOOGLE_FORMS_MESSAGE_FIELD=entry.456789123

# Google reCAPTCHA v3 
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=your_recaptcha_v3_site_key_here
RECAPTCHA_SECRET_KEY=your_recaptcha_v3_secret_key_here

# Security Notes:
# - Never share your secret keys
# - Use strong, unique passwords
# - Regenerate keys if compromised
# - Use environment-specific values for different deployments
