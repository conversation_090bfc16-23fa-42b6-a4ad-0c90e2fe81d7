# 🛡️ reCAPTCHA v3 Setup Guide

## 🚨 **CURRENT STATUS**
reCAPTC<PERSON> is currently **DISABLED** to prevent errors. The contact form will work without reCAPTC<PERSON> protection until you configure it properly.

## 🔧 **QUICK SETUP**

### Step 1: Get reCAPTCHA v3 Keys
1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Click **"+"** to create a new site
3. Choose **reCAPTCHA v3**
4. Add your domain(s):
   - `localhost` (for development)
   - `yourdomain.com` (for production)
5. Copy your **Site Key** and **Secret Key**

### Step 2: Configure Your Portfolio
1. **Update `config/config.php`**:
   ```php
   'recaptcha' => [
       'enabled' => true, // Enable reCAPTCHA
       'site_key' => 'your_actual_site_key_here',
       'secret_key' => '', // Keep empty - use environment variable
   ]
   ```

2. **Update `config/google-forms.js`**:
   ```javascript
   window.RECAPTCHA_CONFIG = {
     enabled: true, // Enable reCAPTCHA
     siteKey: 'your_actual_site_key_here',
   };
   ```

3. **Set environment variable** in `config/.env`:
   ```env
   RECAPTCHA_SECRET_KEY=your_actual_secret_key_here
   ```

### Step 3: Test
1. Clear browser cache
2. Submit contact form
3. Check console for "reCAPTCHA v3 token generated successfully"

## 🔍 **CURRENT CONFIGURATION**

### ✅ **What's Working**
- Contact form submits without reCAPTCHA
- No JavaScript errors
- Graceful degradation when reCAPTCHA is disabled
- Server-side Google Forms backup

### ⚠️ **What's Disabled**
- reCAPTCHA spam protection
- Bot detection
- Automated abuse prevention

## 🛠️ **TROUBLESHOOTING**

### Error: "Invalid site key"
- **Cause**: Using placeholder/example keys
- **Fix**: Replace with real keys from Google reCAPTCHA Admin

### Error: "reCAPTCHA not loaded"
- **Cause**: Network issues or blocked scripts
- **Fix**: Check browser console, disable ad blockers

### Form not submitting
- **Cause**: JavaScript errors
- **Fix**: Check browser console, ensure all files are loaded

## 🎯 **PRODUCTION CHECKLIST**

- [ ] Real reCAPTCHA v3 keys configured
- [ ] Environment variables set
- [ ] Domain added to reCAPTCHA settings
- [ ] Test form submission
- [ ] Check spam protection is working
- [ ] Monitor reCAPTCHA scores in Google Admin

## 🔒 **SECURITY NOTES**

- **Never commit secret keys** to version control
- **Use environment variables** for sensitive data
- **Test thoroughly** before going live
- **Monitor reCAPTCHA scores** to adjust threshold

## 📞 **NEED HELP?**

If you encounter issues:
1. Check browser console for errors
2. Verify keys are correct
3. Test with simple form first
4. Check Google reCAPTCHA admin for usage stats

The contact form will continue working without reCAPTCHA until you're ready to configure it properly! 🚀
